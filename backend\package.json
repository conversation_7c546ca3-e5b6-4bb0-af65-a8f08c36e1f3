{"name": "physioprep-backend", "version": "1.0.0", "description": "PhysioPrep Backend API Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "seed-data": "node scripts/seedData.js"}, "dependencies": {"axios": "^1.12.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "date-fns": "^3.0.6", "dotenv": "^16.5.0", "express": "^4.18.2", "express-validator": "^7.0.1", "google-auth-library": "^10.3.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^2.0.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "nodemon": "^3.1.10", "physioprep-backend": "file:"}, "keywords": ["physioprep", "exam", "preparation", "api", "nodejs", "express", "mongodb"], "author": "PhysioPrep Team", "license": "MIT"}