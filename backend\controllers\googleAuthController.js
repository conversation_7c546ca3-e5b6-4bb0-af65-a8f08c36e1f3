const { OAuth2Client } = require("google-auth-library");
const User = require("../models/User");
const { asyncHandler } = require("../middleware/errorHandler");
const {
  generateAccessToken,
  generateRefreshToken,
} = require("./authController");

// Initialize Google OAuth client
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Verify Google token and authenticate user
const googleAuth = asyncHandler(async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Google token is required" }],
      });
    }

    // Verify the Google token
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    const {
      sub: googleId,
      email,
      name,
      picture: avatar,
      email_verified: emailVerified,
    } = payload;

    if (!emailVerified) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Google email is not verified" }],
      });
    }

    // Check if user exists with this Google ID
    let user = await User.findOne({ googleId });

    if (user) {
      // User exists, update login stats
      await user.updateLoginStats();
    } else {
      // Check if user exists with this email but different provider
      const existingUser = await User.findOne({ email });

      if (existingUser && existingUser.provider === "local") {
        return res.status(400).json({
          success: false,
          errors: [
            {
              msg: "An account with this email already exists. Please sign in with your email and password.",
            },
          ],
        });
      }

      // Create new user
      user = new User({
        name,
        email,
        googleId,
        provider: "google",
        avatar,
        isEmailVerified: true,
      });

      await user.save();
    }

    // Generate tokens
    const accessToken = generateAccessToken(user._id);
    const refreshToken = generateRefreshToken(user._id);

    // Add refresh token to user's refreshTokens array
    user.refreshTokens.push({
      token: refreshToken,
      createdAt: Date.now(),
      expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
      deviceInfo: req.headers["user-agent"],
    });

    // Remove expired tokens
    user.refreshTokens = user.refreshTokens.filter(
      (rt) => rt.expiresAt > new Date()
    );

    await user.save();

    // Get user object without password
    const userObj = await User.findById(user._id).select("-password").lean();

    res.json({
      success: true,
      data: {
        token: accessToken,
        refreshToken,
        user: {
          id: userObj._id,
          name: userObj.name,
          email: userObj.email,
          role: userObj.role,
          isPremium: userObj.isPremium,
          isPremiumActive: user.isPremiumActive,
          isEmailVerified: userObj.isEmailVerified,
          preferences: userObj.preferences,
          stats: userObj.stats,
          avatar: userObj.avatar,
          premiumExpiry: userObj.premiumExpiry,
          provider: userObj.provider,
          createdAt: userObj.createdAt,
          updatedAt: userObj.updatedAt,
        },
      },
    });
  } catch (error) {
    console.error("Google auth error:", error);

    if (error.message && error.message.includes("Token used too early")) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Invalid Google token. Please try again." }],
      });
    }

    res.status(500).json({
      success: false,
      errors: [{ msg: "Google authentication failed" }],
    });
  }
});

// Link Google account to existing local account
const linkGoogleAccount = asyncHandler(async (req, res) => {
  try {
    const { token } = req.body;
    const userId = req.user._id;

    if (!token) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Google token is required" }],
      });
    }

    // Verify the Google token
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    const {
      sub: googleId,
      email: googleEmail,
      picture: avatar,
      email_verified: emailVerified,
    } = payload;

    if (!emailVerified) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Google email is not verified" }],
      });
    }

    // Check if Google account is already linked to another user
    const existingGoogleUser = await User.findOne({ googleId });
    if (
      existingGoogleUser &&
      existingGoogleUser._id.toString() !== userId.toString()
    ) {
      return res.status(400).json({
        success: false,
        errors: [
          { msg: "This Google account is already linked to another user" },
        ],
      });
    }

    // Get current user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        errors: [{ msg: "User not found" }],
      });
    }

    // Check if emails match
    if (user.email !== googleEmail) {
      return res.status(400).json({
        success: false,
        errors: [{ msg: "Google account email must match your current email" }],
      });
    }

    // Link Google account
    user.googleId = googleId;
    if (avatar && !user.avatar) {
      user.avatar = avatar;
    }
    user.isEmailVerified = true;

    await user.save();

    // Get updated user object
    const userObj = await User.findById(user._id).select("-password").lean();

    res.json({
      success: true,
      data: {
        message: "Google account linked successfully",
        user: {
          id: userObj._id,
          name: userObj.name,
          email: userObj.email,
          role: userObj.role,
          isPremium: userObj.isPremium,
          isPremiumActive: user.isPremiumActive,
          isEmailVerified: userObj.isEmailVerified,
          preferences: userObj.preferences,
          stats: userObj.stats,
          avatar: userObj.avatar,
          premiumExpiry: userObj.premiumExpiry,
          provider: userObj.provider,
          createdAt: userObj.createdAt,
          updatedAt: userObj.updatedAt,
        },
      },
    });
  } catch (error) {
    console.error("Link Google account error:", error);
    res.status(500).json({
      success: false,
      errors: [{ msg: "Failed to link Google account" }],
    });
  }
});

module.exports = {
  googleAuth,
  linkGoogleAccount,
};
