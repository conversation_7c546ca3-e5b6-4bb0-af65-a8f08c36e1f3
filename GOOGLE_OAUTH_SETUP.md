# Google OAuth Setup Guide for PhysioPrep

This guide explains how to set up Google OAuth authentication for the PhysioPrep application.

## Overview

The Google OAuth implementation allows users to:
- Sign in with their Google account
- Register new accounts using Google
- Link existing local accounts with Google
- Maintain secure authentication across web and mobile platforms

## Backend Implementation

### Features Added

1. **User Model Updates**
   - Added `googleId` field for storing Google user ID
   - Added `provider` field ('local' or 'google')
   - Made password optional for Google users
   - Auto-verify email for Google users

2. **Google OAuth Controller**
   - `googleAuth`: Verify Google token and authenticate user
   - `linkGoogleAccount`: Link Google account to existing user

3. **API Endpoints**
   - `POST /api/auth/google` - Authenticate with Google token
   - `POST /api/auth/google/link` - Link Google account to existing user

4. **Security Features**
   - **Server-side Google token verification** using Google Auth Library
   - **Proper error handling** for invalid tokens
   - **Prevention of duplicate account linking**
   - **Email verification** automatic for Google users

### Dependencies Added

```bash
npm install google-auth-library
```

## Frontend Implementation

### Features Added

1. **Google Auth Service**
   - OAuth flow management using `useAuthRequest` hook from expo-auth-session
   - Token handling and validation
   - Cross-platform support (iOS, Android, Web)

2. **UI Components**
   - `GoogleSignInButton`: Reusable Google sign-in button using `useAuthRequest`
   - `GoogleLinkButton`: Component for linking Google accounts to existing users
   - `AuthDivider`: Visual separator for auth options
   - Integrated into login and register screens

3. **Authentication Hook Updates**
   - `googleSignIn(idToken)`: Accept Google ID token and authenticate with backend
   - `linkGoogleAccount(idToken)`: Link Google account to existing user
   - Proper error handling and loading states

### Dependencies Added

```bash
npx expo install @react-native-google-signin/google-signin
```

### Implementation Details

The Google OAuth implementation uses the `@react-native-google-signin/google-signin` library, which is the industry-standard approach for Google authentication in React Native applications. This provides:

- **Native Platform Integration**: Uses native Google Sign-In SDKs for better UX
- **Industry Standard**: Widely adopted and maintained library
- **Cross-Platform Support**: Works consistently across iOS, Android, and Web
- **Security**: Implements Google's recommended security practices

#### Key Components:

1. **GoogleSignInButton**: Uses `@react-native-google-signin` for native Google Sign-In
2. **GoogleLinkButton**: Separate component for linking existing accounts
3. **Auth Service**: Utility functions for Google Sign-In configuration and authentication
4. **useAuth Hook**: Updated to handle Google authentication

#### Google Sign-In Flow:

The implementation uses a simplified and secure flow:

1. **Step 1**: App configures Google Sign-In on startup
2. **Step 2**: User taps Google Sign-In button
3. **Step 3**: Native Google Sign-In flow is initiated
4. **Step 4**: Google returns ID token after successful authentication
5. **Step 5**: Frontend sends ID token to backend for verification and user creation/login

**Benefits:**
- Native platform integration for better UX
- Industry-standard security practices
- Simplified authentication flow
- Better reliability and maintenance

## Setup Instructions

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"

#### For Android:
- Application type: Android
- Package name: `com.darshanx01.physioprep`
- SHA-1 certificate fingerprint: (get from `expo credentials:manager`)

#### For iOS:
- Application type: iOS
- Bundle ID: `com.darshanx01.physioprep`

#### For Web:
- Application type: Web application
- Authorized redirect URIs: Add your web domain

### 2. Environment Configuration

#### Backend (.env)
```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
JWT_REFRESH_SECRET=your-refresh-secret-key
```

#### Frontend (.env)
```env
# Google OAuth Configuration
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your-android-client-id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_CLIENT_ID_WEB=your-web-client-id.apps.googleusercontent.com
```

### 3. App Configuration

The app.json already includes the necessary configuration:
```json
{
  "expo": {
    "scheme": "physioprep",
    "plugins": ["expo-web-browser"]
  }
}
```

## Testing

### Backend Testing
Run the test script to verify backend configuration:
```bash
cd backend
node test-google-auth.js
```

### Frontend Testing
1. Ensure environment variables are set
2. Build and run the app
3. Test Google sign-in on login/register screens
4. Verify user creation and authentication flow

## Security Considerations

1. **Token Validation**: All Google tokens are verified server-side
2. **Email Verification**: Google users are automatically email-verified
3. **Account Linking**: Prevents linking Google accounts to wrong users
4. **Error Handling**: Secure error messages without exposing sensitive data

## Troubleshooting

### Common Issues

1. **"Google Client ID not configured"**
   - Ensure environment variables are set correctly
   - Check that the client ID matches your Google Cloud Console setup

2. **"Invalid Google token"**
   - Verify the client ID matches the one used to generate the token
   - Check that the token hasn't expired

3. **"Email already exists"**
   - User tried to sign up with Google using an email that already has a local account
   - Direct them to link their Google account instead

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

## API Response Examples

### Successful Google Authentication
```json
{
  "success": true,
  "data": {
    "token": "jwt-access-token",
    "refreshToken": "jwt-refresh-token",
    "user": {
      "id": "user-id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "provider": "google",
      "isEmailVerified": true,
      "avatar": "https://lh3.googleusercontent.com/...",
      // ... other user fields
    }
  }
}
```

### Error Response
```json
{
  "success": false,
  "errors": [
    {
      "msg": "Google email is not verified"
    }
  ]
}
```

## Future Enhancements

1. **Account Merging**: Allow merging local and Google accounts
2. **Multiple Providers**: Add support for other OAuth providers
3. **Advanced Scopes**: Request additional Google permissions if needed
4. **Offline Access**: Implement refresh token handling for Google APIs
