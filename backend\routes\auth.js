const express = require('express');
const router = express.Router();

// Import controllers
const {
  register,
  login,
  getCurrentUser,
  updateProfile,
  changePassword,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
  refreshUserToken,
  logout,
  logoutAll
} = require('../controllers/authController');

// Import Google OAuth controllers
const {
  googleAuth,
  linkGoogleAccount
} = require('../controllers/googleAuthController');

// Import middleware
const { auth, rateLimitByUser } = require('../middleware/auth');

// Import validators
const {
  validateRegister,
  validateLogin,
  validatePasswordResetRequest,
  validatePasswordReset,
  validateChangePassword,
  validateUpdateProfile,
  validateEmailVerification
} = require('../validators/authValidators');

// Import Google OAuth validators
const {
  validateGoogleAuth,
  validateGoogleLink
} = require('../validators/googleAuthValidators');

// @route   POST /api/auth/register
// @desc    Register user
// @access  Public
router.post('/register', validateRegister, register);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', validateLogin, login);

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', auth, getCurrentUser);

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', auth, validateUpdateProfile, updateProfile);

// @route   PUT /api/auth/change-password
// @desc    Change user password
// @access  Private
router.put('/change-password', changePassword);

// @route   POST /api/auth/request-password-reset
// @desc    Request password reset
// @access  Public
router.post('/request-password-reset', rateLimitByUser(3, 15 * 60 * 1000), validatePasswordResetRequest, requestPasswordReset);

// @route   POST /api/auth/reset-password
// @desc    Reset password with token
// @access  Public
router.post('/reset-password', validatePasswordReset, resetPassword);

// @route   POST /api/auth/verify-email
// @desc    Verify email address
// @access  Public
router.post('/verify-email', validateEmailVerification, verifyEmail);

// @route   POST /api/auth/resend-verification
// @desc    Resend email verification
// @access  Private
router.post('/resend-verification', auth, rateLimitByUser(3, 15 * 60 * 1000), resendVerificationEmail);

// @route   POST /api/auth/refresh
// @desc    Refresh user token
// @access  Public
router.post('/refresh', refreshUserToken);

// @route   POST /api/auth/logout
// @desc    Logout user
// @access  Private
router.post('/logout', auth, logout);

// @route   POST /api/auth/logout-all
// @desc    Logout user from all devices
// @access  Private
router.post('/logout-all', auth, logoutAll);

// Google OAuth Routes
// @route   POST /api/auth/google
// @desc    Authenticate with Google
// @access  Public
router.post('/google', validateGoogleAuth, googleAuth);

// @route   POST /api/auth/google/link
// @desc    Link Google account to existing user
// @access  Private
router.post('/google/link', auth, validateGoogleLink, linkGoogleAccount);

module.exports = router;
