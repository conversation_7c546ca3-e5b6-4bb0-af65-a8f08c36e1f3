const { OAuth2Client } = require('google-auth-library');
const User = require('./models/User');
require('dotenv').config();

// Test Google OAuth functionality
async function testGoogleAuth() {
  console.log('Testing Google OAuth Configuration...\n');

  // Test 1: Check environment variables
  console.log('1. Checking environment variables:');
  const googleClientId = process.env.GOOGLE_CLIENT_ID;
  const jwtSecret = process.env.JWT_SECRET;
  const jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;

  console.log(`   GOOGLE_CLIENT_ID: ${googleClientId ? '✓ Set' : '✗ Missing'}`);
  console.log(`   JWT_SECRET: ${jwtSecret ? '✓ Set' : '✗ Missing'}`);
  console.log(`   JWT_REFRESH_SECRET: ${jwtRefreshSecret ? '✓ Set' : '✗ Missing'}\n`);

  if (!googleClientId) {
    console.log('❌ Google Client ID is not configured. Please set GOOGLE_CLIENT_ID in .env file');
    return;
  }

  // Test 2: Initialize Google OAuth client
  console.log('2. Testing Google OAuth client initialization:');
  try {
    const client = new OAuth2Client(googleClientId);
    console.log('   ✓ Google OAuth client initialized successfully\n');
  } catch (error) {
    console.log(`   ✗ Failed to initialize Google OAuth client: ${error.message}\n`);
    return;
  }

  // Test 3: Test User model with Google fields
  console.log('3. Testing User model with Google OAuth fields:');
  try {
    const testUser = new User({
      name: 'Test Google User',
      email: '<EMAIL>',
      googleId: 'test-google-id-123',
      provider: 'google',
      isEmailVerified: true,
    });

    // Validate the user without saving
    await testUser.validate();
    console.log('   ✓ User model validation passed for Google user\n');
  } catch (error) {
    console.log(`   ✗ User model validation failed: ${error.message}\n`);
  }

  // Test 4: Test User model with local user (should require password)
  console.log('4. Testing User model validation for local user:');
  try {
    const localUser = new User({
      name: 'Test Local User',
      email: '<EMAIL>',
      provider: 'local',
      // Missing password - should fail validation
    });

    await localUser.validate();
    console.log('   ✗ Local user validation should have failed (missing password)\n');
  } catch (error) {
    if (error.message.includes('Password is required')) {
      console.log('   ✓ Local user validation correctly requires password\n');
    } else {
      console.log(`   ✗ Unexpected validation error: ${error.message}\n`);
    }
  }

  console.log('Google OAuth backend configuration test completed!');
  console.log('\nNext steps:');
  console.log('1. Set up Google OAuth credentials in Google Cloud Console');
  console.log('2. Update GOOGLE_CLIENT_ID in backend/.env');
  console.log('3. Update EXPO_PUBLIC_GOOGLE_CLIENT_ID in frontend/.env');
  console.log('4. Test the complete OAuth flow with a real Google account');
}

// Run the test
if (require.main === module) {
  testGoogleAuth().catch(console.error);
}

module.exports = { testGoogleAuth };
