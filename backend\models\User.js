const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
      maxlength: [50, "Name cannot exceed 50 characters"],
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        "Please enter a valid email",
      ],
    },
    password: {
      type: String,
      required: function() {
        return this.provider === 'local';
      },
      minlength: [8, "Password must be at least 8 characters long"],
      select: false,
    },
    // Google OAuth fields
    googleId: {
      type: String,
      unique: true,
      sparse: true, // Allows multiple null values
    },
    provider: {
      type: String,
      enum: ['local', 'google'],
      default: 'local',
    },
    role: {
      type: String,
      enum: ["user", "admin"],
      default: "user",
    },
    isPremium: {
      type: Boolean,
      default: false,
    },
    premiumExpiry: {
      type: Date,
    },

    refreshTokens: [
      {
        token: String,
        createdAt: { type: Date, default: Date.now },
        expiresAt: Date,
        deviceInfo: String, // Optional: track devices
      },
    ],

    // Track individual question performance
    questionAttempts: [
      {
        questionId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Question",
          required: true,
        },
        subject: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Subject",
          required: false,
          default: null,
        },
        isCorrect: {
          type: Boolean,
          required: true,
        },
        timeSpent: {
          type: Number, // seconds
          default: 0,
        },
        attemptedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // Keep existing fields but these won't be used for question filtering
    quizHistory: [
      {
        quiz: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Quiz",
        },
        score: { type: Number },
        timeSpent: { type: Number },
        answers: { type: [Number] },
        completedAt: { type: Date },
      },
    ],
    testHistory: [
      {
        testId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Test",
        },
        score: Number,
        completedAt: Date,
      },
    ],
    avatar: {
      type: String,
      default: null,
    },
    isEmailVerified: {
      type: Boolean,
      default: function() {
        return this.provider === 'google';
      },
    },
    emailVerificationToken: {
      type: String,
      default: null,
    },
    passwordResetToken: {
      type: String,
      default: null,
    },
    passwordResetExpires: {
      type: Date,
      default: null,
    },
    lastLogin: {
      type: Date,
      default: null,
    },
    loginCount: {
      type: Number,
      default: 0,
    },
    preferences: {
      notifications: {
        type: Boolean,
        default: true,
      },
      dailyReminder: {
        type: Boolean,
        default: true,
      },
      theme: {
        type: String,
        enum: ["light", "dark", "auto"],
        default: "auto",
      },
    },
    stats: {
      totalQuizzesTaken: {
        type: Number,
        default: 0,
      },
      totalTestsTaken: {
        type: Number,
        default: 0,
      },
      totalQuestionsAnswered: {
        type: Number,
        default: 0,
      },
      correctAnswers: {
        type: Number,
        default: 0,
      },
      averageScore: {
        type: Number,
        default: 0,
      },
      // Overall activity streak across the app
      currentStreak: {
        type: Number,
        default: 0,
      },
      longestStreak: {
        type: Number,
        default: 0,
      },
      lastActivityDate: {
        type: Date,
        default: null,
      },
      // Daily question specific streaks
      dailyQuestionCurrentStreak: {
        type: Number,
        default: 0,
      },
      dailyQuestionLongestStreak: {
        type: Number,
        default: 0,
      },
      lastDailyQuestionDate: {
        type: Date,
        default: null,
      },
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual for accuracy percentage
userSchema.virtual("accuracyPercentage").get(function () {
  if (this.stats.totalQuestionsAnswered === 0) return 0;
  return Math.round(
    (this.stats.correctAnswers / this.stats.totalQuestionsAnswered) * 100
  );
});

// Virtual for premium status check
userSchema.virtual("isPremiumActive").get(function () {
  if (!this.isPremium) return false;
  if (!this.premiumExpiry) return false;
  return new Date() < this.premiumExpiry;
});

// Pre-save middleware to hash password
userSchema.pre("save", async function (next) {
  // Skip password hashing for Google users or if password is not modified
  if (!this.isModified("password") || this.provider === 'google') {
    return next();
  }

  // Only hash password for local users
  if (this.password) {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
  }
  next();
});

// Method to compare password
userSchema.methods.comparePassword = async function (candidatePassword) {
  // Google users don't have passwords to compare
  if (this.provider === 'google') {
    return false;
  }
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to update login stats
userSchema.methods.updateLoginStats = function () {
  this.lastLogin = new Date();
  this.loginCount += 1;
  return this.save();
};

// Method to record question attempt
userSchema.methods.recordQuestionAttempt = function (
  questionId,
  isCorrect,
  subjectId = null,
  timeSpent = 0
) {
  this.questionAttempts.push({
    questionId,
    subject: subjectId,
    isCorrect,
    timeSpent,
    attemptedAt: new Date(),
  });
  return this.save();
};

// Method to get correctly answered question IDs
userSchema.methods.getCorrectlyAnsweredQuestions = function () {
  return this.questionAttempts
    .filter((attempt) => attempt.isCorrect)
    .map((attempt) => attempt.questionId.toString());
};

// Method to update activity streak
userSchema.methods.updateStreak = function () {
  const today = new Date();
  const lastActivity = this.stats.lastActivityDate;

  if (!lastActivity) {
    // First activity
    this.stats.currentStreak = 1;
    this.stats.longestStreak = Math.max(this.stats.longestStreak, 1);
  } else {
    const daysDiff = Math.floor((today - lastActivity) / (1000 * 60 * 60 * 24));

    if (daysDiff === 1) {
      // Consecutive day
      this.stats.currentStreak += 1;
      this.stats.longestStreak = Math.max(
        this.stats.longestStreak,
        this.stats.currentStreak
      );
    } else if (daysDiff > 1) {
      // Streak broken
      this.stats.currentStreak = 1;
    }
    // If daysDiff === 0, same day activity, don't change streak
  }

  this.stats.lastActivityDate = today;
  return this.save();
};

// Sign JWT and return
userSchema.methods.getSignedJwtToken = function () {
  return jwt.sign({ id: this._id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE,
  });
};

// Index for premium users
userSchema.index({ isPremium: 1, premiumExpiry: 1 });

// Index for provider (googleId already has unique index)
userSchema.index({ provider: 1 });

// Index for question attempts to improve query performance
userSchema.index({
  "questionAttempts.questionId": 1,
  "questionAttempts.isCorrect": 1,
});
userSchema.index({ "questionAttempts.attemptedAt": -1 });

const User = mongoose.model("User", userSchema);

module.exports = User;
