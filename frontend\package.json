{"name": "physioprep", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "start-server": "cd ../backend && nodemon server.js", "start-cache": "expo start -c", "test-tscon": "npx tsc --noEmit", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "seed-data": "cd ../backend && node scripts/seedData.js", "reset-project": "node ./scripts/reset-project.js"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-native-google-signin/google-signin": "^16.0.0", "@react-native-menu/menu": "^1.2.3", "@shopify/flash-list": "1.7.6", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "axios": "^1.10.0", "clsx": "^2.1.1", "expo": "^53.0.11", "expo-auth-session": "~6.2.1", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-router": "~5.1.0", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "jotai": "^2.12.5", "nativewind": "latest", "react": "19.0.0", "react-native": "0.79.3", "react-native-actions-sheet": "^0.9.7", "react-native-gesture-handler": "~2.24.0", "react-native-popup-menu": "^0.18.0", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-webview": "13.13.5", "tailwind-merge": "^3.3.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "private": true}