const { body } = require('express-validator');

const validateGoogleAuth = [
  body('token')
    .notEmpty()
    .withMessage('Google token is required')
    .isString()
    .withMessage('Google token must be a string')
    .isLength({ min: 10 })
    .withMessage('Invalid Google token format'),
];

const validateGoogleLink = [
  body('token')
    .notEmpty()
    .withMessage('Google token is required')
    .isString()
    .withMessage('Google token must be a string')
    .isLength({ min: 10 })
    .withMessage('Invalid Google token format'),
];

module.exports = {
  validateGoogleAuth,
  validateGoogleLink,
};
